; AESUtils.ahk
; AES Encryption/Decryption Utility for AutoHotkey v2
; Converted from Java version

#Requires AutoHotkey v2.0

class AESUtils {
    ; Default base64 encoded secret key
    static BASE64_SECRET := "SnlrajE5OTRKeWtqMTk5NA=="

    ; Encrypt content using AES
    static AESEnCode(content, secretKey := "") {
        if (secretKey == "")
            secretKey := this.BASE64_SECRET

        ; Decode the base64 key
        binaryKey := this.Base64Decode(secretKey)

        ; Convert content to UTF-8 bytes
        contentBytes := this.StringToBytes(content)

        ; Generate random IV (16 bytes)
        iv := Buffer(16)
        DllCall("BCrypt\BCryptGenRandom", "Ptr", 0, "Ptr", iv.Ptr, "UInt", 16, "UInt", 0x00000002)

        ; Encrypt the data
        encrypted := this.EncryptData(binaryKey, contentBytes, iv)

        ; Combine IV + encrypted data
        result := Buffer(16 + encrypted.Size)
        DllCall("RtlMoveMemory", "Ptr", result.Ptr, "Ptr", iv.Ptr, "UInt", 16)
        DllCall("RtlMoveMemory", "Ptr", result.Ptr + 16, "Ptr", encrypted.Ptr, "UInt", encrypted.Size)

        ; Return the encrypted hex string
        return this.BinaryToHex(result)
    }

    ; Decrypt content using AES
    static AESDeCode(ciphertext, secretKey := "") {
        if (secretKey == "")
            secretKey := this.BASE64_SECRET

        ; Decode the base64 key
        binaryKey := this.Base64Decode(secretKey)

        ; Convert hex to binary
        encryptedData := this.HexToBinary(ciphertext)

        ; Extract IV (first 16 bytes)
        iv := Buffer(16)
        DllCall("RtlMoveMemory", "Ptr", iv.Ptr, "Ptr", encryptedData.Ptr, "UInt", 16)

        ; Extract encrypted content (remaining bytes)
        encryptedContent := Buffer(encryptedData.Size - 16)
        DllCall("RtlMoveMemory", "Ptr", encryptedContent.Ptr, "Ptr", encryptedData.Ptr + 16, "UInt", encryptedContent.Size)

        ; Decrypt the data
        decrypted := this.DecryptData(binaryKey, encryptedContent, iv)

        ; Convert the decrypted bytes to string
        return this.BytesToString(decrypted)
    }

    ; Helper function to encrypt data using AES
    static EncryptData(key, data, iv) {
        ; Create encryption algorithm provider
        hAlg := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptOpenAlgorithmProvider", "UPtr*", hAlg.Ptr, "Str", "AES", "Ptr", 0, "UInt", 0)
        hAlg := NumGet(hAlg, 0, "Ptr")

        ; Set chaining mode to CBC
        DllCall("BCrypt\BCryptSetProperty", "Ptr", hAlg, "Str", "ChainingMode", "Str", "ChainingModeCBC", "UInt", 32, "UInt", 0)

        ; Create key object
        keyObj := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptGenerateSymmetricKey", "Ptr", hAlg, "UPtr*", keyObj.Ptr, "Ptr", 0, "UInt", 0, "Ptr", key.Ptr, "UInt", key.Size, "UInt", 0)
        keyObj := NumGet(keyObj, 0, "Ptr")

        ; Calculate output buffer size
        cbResult := Buffer(4)
        DllCall("BCrypt\BCryptEncrypt", "Ptr", keyObj, "Ptr", data.Ptr, "UInt", data.Size, "Ptr", 0, "Ptr", iv.Ptr, "UInt", 16, "Ptr", 0, "UInt", 0, "Ptr", cbResult.Ptr, "UInt", 0)
        cbResult := NumGet(cbResult, 0, "UInt")

        ; Create output buffer
        outputBuffer := Buffer(cbResult)

        ; Encrypt
        cbResultOut := Buffer(4)
        DllCall("BCrypt\BCryptEncrypt", "Ptr", keyObj, "Ptr", data.Ptr, "UInt", data.Size, "Ptr", 0, "Ptr", iv.Ptr, "UInt", 16, "Ptr", outputBuffer.Ptr, "UInt", cbResult, "Ptr", cbResultOut.Ptr, "UInt", 0)

        ; Clean up
        DllCall("BCrypt\BCryptDestroyKey", "Ptr", keyObj)
        DllCall("BCrypt\BCryptCloseAlgorithmProvider", "Ptr", hAlg, "UInt", 0)

        return outputBuffer
    }

    ; Helper function to decrypt data using AES
    static DecryptData(key, data, iv) {
        ; Create encryption algorithm provider
        hAlg := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptOpenAlgorithmProvider", "UPtr*", hAlg.Ptr, "Str", "AES", "Ptr", 0, "UInt", 0)
        hAlg := NumGet(hAlg, 0, "Ptr")

        ; Set chaining mode to CBC
        DllCall("BCrypt\BCryptSetProperty", "Ptr", hAlg, "Str", "ChainingMode", "Str", "ChainingModeCBC", "UInt", 32, "UInt", 0)

        ; Create key object
        keyObj := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptGenerateSymmetricKey", "Ptr", hAlg, "UPtr*", keyObj.Ptr, "Ptr", 0, "UInt", 0, "Ptr", key.Ptr, "UInt", key.Size, "UInt", 0)
        keyObj := NumGet(keyObj, 0, "Ptr")

        ; Calculate output buffer size
        cbResult := Buffer(4)
        DllCall("BCrypt\BCryptDecrypt", "Ptr", keyObj, "Ptr", data.Ptr, "UInt", data.Size, "Ptr", 0, "Ptr", iv.Ptr, "UInt", 16, "Ptr", 0, "UInt", 0, "Ptr", cbResult.Ptr, "UInt", 0)
        cbResult := NumGet(cbResult, 0, "UInt")

        ; Create output buffer
        outputBuffer := Buffer(cbResult)

        ; Decrypt
        cbResultOut := Buffer(4)
        DllCall("BCrypt\BCryptDecrypt", "Ptr", keyObj, "Ptr", data.Ptr, "UInt", data.Size, "Ptr", 0, "Ptr", iv.Ptr, "UInt", 16, "Ptr", outputBuffer.Ptr, "UInt", cbResult, "Ptr", cbResultOut.Ptr, "UInt", 0)

        ; Clean up
        DllCall("BCrypt\BCryptDestroyKey", "Ptr", keyObj)
        DllCall("BCrypt\BCryptCloseAlgorithmProvider", "Ptr", hAlg, "UInt", 0)

        return outputBuffer
    }

    ; Helper function to decode Base64 to binary
    static Base64Decode(base64String) {
        ; Calculate decoded size
        decodedSize := this.Base64DecodeSize(base64String)

        ; Create buffer for decoded data
        decoded := Buffer(decodedSize)

        ; Decode base64
        if (DllCall("crypt32\CryptStringToBinary", "Str", base64String, "UInt", 0, "UInt", 0x1, "Ptr", decoded.Ptr, "UInt*", decodedSize, "Ptr", 0, "Ptr", 0))
            return decoded

        return ""
    }

    ; Calculate the size required for base64 decoding
    static Base64DecodeSize(base64String) {
        requiredSize := 0
        DllCall("crypt32\CryptStringToBinary", "Str", base64String, "UInt", 0, "UInt", 0x1, "Ptr", 0, "UInt*", &requiredSize, "Ptr", 0, "Ptr", 0)
        return requiredSize
    }

    ; Convert binary data to hex string
    static BinaryToHex(binary) {
        if !(binary is Buffer)
            return ""

        result := ""
        loop binary.Size {
            byte := NumGet(binary, A_Index-1, "UChar")
            result .= Format("{:02x}", byte)
        }
        return result
    }

    ; Convert hex string to binary data
    static HexToBinary(hexString) {
        if (Mod(StrLen(hexString), 2) != 0)
            return ""

        binary := Buffer(StrLen(hexString) // 2)

        loop binary.Size {
            byte := "0x" . SubStr(hexString, (A_Index * 2) - 1, 2)
            NumPut("UChar", Integer(byte), binary, A_Index-1)
        }

        return binary
    }

    ; Convert string to UTF-8 bytes with PKCS7 padding
    static StringToBytes(str) {
        ; Calculate required buffer size
        size := StrPut(str, "UTF-8") - 1  ; -1 to exclude null terminator

        ; Calculate padded size (AES block size is 16 bytes)
        blockSize := 16
        paddedSize := size + (blockSize - Mod(size, blockSize))

        ; Create buffer and convert string
        dataBuffer := Buffer(paddedSize)
        StrPut(str, dataBuffer, "UTF-8")

        ; Add PKCS7 padding
        paddingValue := paddedSize - size
        loop paddingValue {
            NumPut("UChar", paddingValue, dataBuffer, size + A_Index - 1)
        }

        return dataBuffer
    }

    ; Convert UTF-8 bytes to string (remove PKCS7 padding)
    static BytesToString(bytes) {
        if (bytes.Size == 0)
            return ""

        ; Get the padding value from the last byte
        paddingValue := NumGet(bytes, bytes.Size - 1, "UChar")

        ; Validate padding
        if (paddingValue > 16 || paddingValue == 0)
            return StrGet(bytes.Ptr, bytes.Size, "UTF-8")

        ; Check if all padding bytes are correct
        validPadding := true
        loop paddingValue {
            if (NumGet(bytes, bytes.Size - A_Index, "UChar") != paddingValue) {
                validPadding := false
                break
            }
        }

        ; Remove padding if valid
        if (validPadding) {
            actualSize := bytes.Size - paddingValue
            return StrGet(bytes.Ptr, actualSize, "UTF-8")
        } else {
            return StrGet(bytes.Ptr, bytes.Size, "UTF-8")
        }
    }
}

; Example usage
if (A_ScriptName = "AESUtils.ahk") {
    ; This code only runs when this script is directly executed (not when included/imported)
    textInput := "hello world"
    MsgBox("Original text: " . textInput)

    enCode := AESUtils.AESEnCode(textInput)
    MsgBox("Encrypted text: " . enCode)

    dnCode := AESUtils.AESDeCode(enCode)
    MsgBox("Decrypted text: " . dnCode)
}