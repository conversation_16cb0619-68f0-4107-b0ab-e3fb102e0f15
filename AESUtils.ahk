; AESUtils.ahk
; AES Encryption/Decryption Utility for AutoHotkey v2
; Converted from Java version

#Requires AutoHotkey v2.0

class AESUtils {
    ; Default base64 encoded secret key
    static BASE64_SECRET := "SnlrajE5OTRKeWtqMTk5NA=="
    
    ; Encrypt content using AES
    static AESEnCode(content, secretKey := "") {
        if (secretKey == "")
            secretKey := this.BASE64_SECRET
        
        ; Decode the base64 key
        binaryKey := this.Base64Decode(secretKey)
        
        ; Create encryption object
        encrypter := Buffer(16 + StrPut(content, "UTF-8"))
        
        ; Generate random IV (16 bytes)
        DllCall("BCrypt\BCryptGenRandom", "Ptr", 0, "Ptr", encrypter.Ptr, "UInt", 16, "UInt", 0x00000002)
        
        ; Copy content to buffer after IV
        StrPut(content, encrypter.Ptr + 16, "UTF-8")
        
        ; Encrypt the data
        encrypted := this.EncryptData(binaryKey, encrypter)
        
        ; Return the encrypted hex string
        return this.BinaryToHex(encrypted)
    }
    
    ; Decrypt content using AES
    static AESDeCode(ciphertext, secretKey := "") {
        if (secretKey == "")
            secretKey := this.BASE64_SECRET
        
        ; Decode the base64 key
        binaryKey := this.Base64Decode(secretKey)
        
        ; Convert hex to binary
        encrypted := this.HexToBinary(ciphertext)
        
        ; Decrypt the data
        decrypted := this.DecryptData(binaryKey, encrypted)
        
        ; Extract IV and content
        iv := Buffer(16)
        DllCall("RtlMoveMemory", "Ptr", iv.Ptr, "Ptr", decrypted.Ptr, "UInt", 16)
        
        ; Convert the decrypted text (skip first 16 bytes which is the IV)
        return StrGet(decrypted.Ptr + 16, "UTF-8")
    }
    
    ; Helper function to encrypt data using AES
    static EncryptData(key, data) {
        ; Create encryption algorithm provider
        hAlg := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptOpenAlgorithmProvider", "UPtr*", hAlg, "Str", "AES", "Ptr", 0, "UInt", 0)
        hAlg := NumGet(hAlg, 0, "Ptr")
        
        ; Create key object
        keyObj := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptGenerateSymmetricKey", "Ptr", hAlg, "UPtr*", keyObj, "Ptr", 0, "UInt", 0, "Ptr", key.Ptr, "UInt", key.Size, "UInt", 0)
        keyObj := NumGet(keyObj, 0, "Ptr")
        
        ; Calculate output buffer size
        cbResult := Buffer(4)
        DllCall("BCrypt\BCryptEncrypt", "Ptr", keyObj, "Ptr", data.Ptr + 16, "UInt", data.Size - 16, "Ptr", 0, "Ptr", data.Ptr, "UInt", 16, "Ptr", 0, "UInt", 0, "Ptr", cbResult, "UInt", 0)
        cbResult := NumGet(cbResult, 0, "UInt")
        
        ; Create output buffer
        outputBuffer := Buffer(16 + cbResult) ; IV + encrypted data
        
        ; Copy IV to output
        DllCall("RtlMoveMemory", "Ptr", outputBuffer.Ptr, "Ptr", data.Ptr, "UInt", 16)
        
        ; Encrypt
        DllCall("BCrypt\BCryptEncrypt", "Ptr", keyObj, "Ptr", data.Ptr + 16, "UInt", data.Size - 16, "Ptr", 0, "Ptr", data.Ptr, "UInt", 16, "Ptr", outputBuffer.Ptr + 16, "UInt", cbResult, "Ptr", cbResult, "UInt", 0)
        
        ; Clean up
        DllCall("BCrypt\BCryptDestroyKey", "Ptr", keyObj)
        DllCall("BCrypt\BCryptCloseAlgorithmProvider", "Ptr", hAlg, "UInt", 0)
        
        return outputBuffer
    }
    
    ; Helper function to decrypt data using AES
    static DecryptData(key, data) {
        ; Create encryption algorithm provider
        hAlg := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptOpenAlgorithmProvider", "UPtr*", hAlg, "Str", "AES", "Ptr", 0, "UInt", 0)
        hAlg := NumGet(hAlg, 0, "Ptr")
        
        ; Create key object
        keyObj := Buffer(A_PtrSize)
        DllCall("BCrypt\BCryptGenerateSymmetricKey", "Ptr", hAlg, "UPtr*", keyObj, "Ptr", 0, "UInt", 0, "Ptr", key.Ptr, "UInt", key.Size, "UInt", 0)
        keyObj := NumGet(keyObj, 0, "Ptr")
        
        ; Calculate output buffer size
        cbResult := Buffer(4)
        DllCall("BCrypt\BCryptDecrypt", "Ptr", keyObj, "Ptr", data.Ptr + 16, "UInt", data.Size - 16, "Ptr", 0, "Ptr", data.Ptr, "UInt", 16, "Ptr", 0, "UInt", 0, "Ptr", cbResult, "UInt", 0)
        cbResult := NumGet(cbResult, 0, "UInt")
        
        ; Create output buffer
        outputBuffer := Buffer(16 + cbResult) ; IV + decrypted data
        
        ; Copy IV to output
        DllCall("RtlMoveMemory", "Ptr", outputBuffer.Ptr, "Ptr", data.Ptr, "UInt", 16)
        
        ; Decrypt
        DllCall("BCrypt\BCryptDecrypt", "Ptr", keyObj, "Ptr", data.Ptr + 16, "UInt", data.Size - 16, "Ptr", 0, "Ptr", data.Ptr, "UInt", 16, "Ptr", outputBuffer.Ptr + 16, "UInt", cbResult, "Ptr", cbResult, "UInt", 0)
        
        ; Clean up
        DllCall("BCrypt\BCryptDestroyKey", "Ptr", keyObj)
        DllCall("BCrypt\BCryptCloseAlgorithmProvider", "Ptr", hAlg, "UInt", 0)
        
        return outputBuffer
    }
    
    ; Helper function to decode Base64 to binary
    static Base64Decode(base64String) {
        ; Calculate decoded size
        decodedSize := this.Base64DecodeSize(base64String)
        
        ; Create buffer for decoded data
        decoded := Buffer(decodedSize)
        
        ; Decode base64
        if (DllCall("crypt32\CryptStringToBinary", "Str", base64String, "UInt", 0, "UInt", 0x1, "Ptr", decoded.Ptr, "UInt*", decodedSize, "Ptr", 0, "Ptr", 0))
            return decoded
        
        return ""
    }
    
    ; Calculate the size required for base64 decoding
    static Base64DecodeSize(base64String) {
        requiredSize := 0
        DllCall("crypt32\CryptStringToBinary", "Str", base64String, "UInt", 0, "UInt", 0x1, "Ptr", 0, "UInt*", &requiredSize, "Ptr", 0, "Ptr", 0)
        return requiredSize
    }
    
    ; Convert binary data to hex string
    static BinaryToHex(binary) {
        if !(binary is Buffer)
            return ""
        
        result := ""
        loop binary.Size {
            byte := NumGet(binary, A_Index-1, "UChar")
            result .= Format("{:02x}", byte)
        }
        return result
    }
    
    ; Convert hex string to binary data
    static HexToBinary(hexString) {
        if (Mod(StrLen(hexString), 2) != 0)
            return ""
            
        binary := Buffer(StrLen(hexString) // 2)
        
        loop binary.Size {
            byte := "0x" . SubStr(hexString, (A_Index * 2) - 1, 2)
            NumPut("UChar", Integer(byte), binary, A_Index-1)
        }
        
        return binary
    }
}

; Example usage
if (A_ScriptName = "AESUtils.ahk") {
    ; This code only runs when this script is directly executed (not when included/imported)
    textInput := "hello world"
    MsgBox("Original text: " . textInput)
    
    enCode := AESUtils.AESEnCode(textInput)
    MsgBox("Encrypted text: " . enCode)
    
    dnCode := AESUtils.AESDeCode(enCode)
    MsgBox("Decrypted text: " . dnCode)
} 