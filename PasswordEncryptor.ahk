#Requires AutoHotkey v2.0
#Include "AESUtils.ahk"

; Create a GUI for password encryption
PasswordApp := Gui(, "密码加密工具")
PasswordApp.SetFont("s10")

; Add GUI elements
PasswordApp.AddText("xm y+20 w120", "请输入密码：")
passwordInput := PasswordApp.AddEdit("x+10 yp-3 w200 Password")

PasswordApp.AddText("xm y+20 w120", "加密结果：")
encryptedResult := PasswordApp.AddEdit("x+10 yp-3 w300 ReadOnly")

PasswordApp.AddText("xm y+20 w120", "解密结果：")
decryptedResult := PasswordApp.AddEdit("x+10 yp-3 w200 ReadOnly")

; Add buttons
encryptBtn := PasswordApp.AddButton("xm y+20 w100", "加密").OnEvent("Click", EncryptPassword)
decryptBtn := PasswordApp.AddButton("x+10 yp w100", "解密").OnEvent("Click", DecryptPassword)
copyBtn := PasswordApp.AddButton("x+10 yp w100", "复制结果").OnEvent("Click", CopyResult)
clearBtn := PasswordApp.AddButton("x+10 yp w100", "清空").OnEvent("Click", ClearFields)

; Show the GUI
PasswordApp.Show()

; Function to encrypt the password
EncryptPassword(*)
{
    password := passwordInput.Value
    
    if (password != "")
    {
        encryptedText := AESUtils.AESEnCode(password)
        encryptedResult.Value := encryptedText
        decryptedResult.Value := ""
    }
    else
    {
        MsgBox("请输入密码！", "提示", "Icon!")
    }
}

; Function to decrypt the encrypted text
DecryptPassword(*)
{
    encryptedText := encryptedResult.Value
    
    if (encryptedText != "")
    {
        try
        {
            decryptedText := AESUtils.AESDeCode(encryptedText)
            decryptedResult.Value := decryptedText
        }
        catch as err
        {
            MsgBox("解密失败！" . err.Message, "错误", "Icon!")
        }
    }
    else
    {
        MsgBox("请先加密密码！", "提示", "Icon!")
    }
}

; Function to copy the encrypted result to clipboard
CopyResult(*)
{
    if (encryptedResult.Value != "")
    {
        A_Clipboard := encryptedResult.Value
        ToolTip("已复制到剪贴板！")
        SetTimer () => ToolTip(), -1000
    }
}

; Function to clear all fields
ClearFields(*)
{
    passwordInput.Value := ""
    encryptedResult.Value := ""
    decryptedResult.Value := ""
} 