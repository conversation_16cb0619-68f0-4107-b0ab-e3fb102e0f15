# AutoHotkey自动化工具集

这是一套基于AutoHotkey v2开发的自动化工具集，用于简化各种系统操作和登录流程。

## 功能列表

1. **门户网站自动登录 (loginPortalV2.ahk)**
   - 自动获取验证码并登录门户网站
   - 提供图形界面输入用户名和密码
   - 显示登录状态

2. **TY系统自动登录 (openTYV2.ahk)**
   - 自动打开TY系统并登录
   - 支持账号密码登录

3. **SSO系统自动登录 (openSSOV2.ahk)**
   - 自动打开SSO系统并登录

4. **MDM系统自动登录 (openMDMV2.ahk)**
   - 自动打开MDM系统并登录

5. **EasyCon自动登录 (openEasyConV2.ahk)**
   - 自动打开EasyCon系统并登录

6. **其他工具**
   - 关闭所有Web页面 (closeallWeb.ahk)
   - OCR识别 (ocr.ahk)

## 使用方法

### 门户网站自动登录 (loginPortalV2.ahk)

1. 双击运行`loginPortalV2.ahk`
2. 在弹出的界面中输入用户名和密码
3. 点击"登录"按钮
4. 系统会自动获取验证码并进行登录
5. 登录状态会显示在界面底部

### 配置说明

在每个脚本文件的开头部分，您可以根据需要修改以下配置：

```autohotkey
; 配置区域 - 可根据需要修改
username := "admin"  ; 替换为实际用户名
password := "password"  ; 替换为实际密码
portal_url := "http://172.16.33.254:8008"  ; 替换为实际的服务器地址
```

## 系统要求

- Windows 10或更高版本
- AutoHotkey v2.0或更高版本
- 网络连接正常

## 常见问题

### 登录失败

1. 检查用户名和密码是否正确
2. 检查网络连接是否正常
3. 检查服务器地址是否正确
4. 查看状态信息，了解具体错误原因

### 验证码获取失败

1. 检查网络连接
2. 确认验证码接口是否可用
3. 重试登录操作

## 开发说明

所有脚本使用AutoHotkey v2语法开发，主要使用了以下技术：

- GUI界面创建
- HTTP请求发送与处理
- JSON数据解析
- 字符串编码与加密
- 事件处理

如需添加新功能或修改现有功能，请参考各个脚本的代码结构和注释。 