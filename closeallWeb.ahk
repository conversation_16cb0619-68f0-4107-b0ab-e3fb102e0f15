#Requires AutoHotkey v2.0
; 定义浏览器进程名列表
; 关闭所有浏览器

; 关闭所有浏览器 (适用于 AutoHotkey v2.0)

; 关闭 Chrome
if WinExist("ahk_exe chrome.exe")
    WinClose("ahk_exe chrome.exe")

; 关闭 Firefox
if WinExist("ahk_exe firefox.exe")
    WinClose("ahk_exe firefox.exe")

; 关闭 Edge
if WinExist("ahk_exe msedge.exe")
    WinClose("ahk_exe msedge.exe")

; 关闭 Opera
if WinExist("ahk_exe opera.exe")
    WinClose("ahk_exe opera.exe")

if WinExist("J2E v0.4.0-test")
    WinClose("J2E v0.4.0-test")