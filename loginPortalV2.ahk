#Requires AutoHotkey v2.0
; 门户网站登录脚本 - 基于接口.md中的接口
; 作者: Claude
; 日期: 2023-11-09
; 功能: 自动调用验证码接口获取验证码，然后进行登录
; 更新: 移除GUI界面，直接自动执行登录
; 更新: 修复类型不匹配问题，移除不必要的编码函数
; 直接使用加密后的用户名和密码
; 完全自动化执行，无需用户交互

; 配置区域 - 可根据需要修改
portal_url := "http://172.16.33.254:8008"
verify_code_url := portal_url "/user_auth_verify.cgi"
login_url := portal_url "/portal.cgi"
enableLogging := true  ; 是否启用日志记录

; 默认登录凭据 - 直接使用加密后的值
defaultUsername := "d3DFZ2trQfYX536wOBsQKQ=="  ; 默认加密后的用户名 hj
defaultPassword := "2XTqwL5xTifTMQ2u5JqTlw=="  ; 默认加密后的密码

defaultUsername := "le8z/BB0/bNYF8heit2eZw=="  ; 默认加密后的用户名 lyl
defaultPassword := "W+yohtpNKxMDwQJsbEO8ZA=="  ; 默认加密后的密码

; 检查是否提供了命令行参数
if (A_Args.Length >= 2) {
    ; 使用命令行参数中的用户名和密码
    encryptedUsername := A_Args[1]
    encryptedPassword := A_Args[2]
    
    if (enableLogging)
        FileAppend "使用命令行参数提供的凭据`n", "portal_login.log", "UTF-8"
} else {
    ; 使用默认值，不显示任何对话框
    encryptedUsername := defaultUsername
    encryptedPassword := defaultPassword
    
    if (enableLogging)
        FileAppend "使用默认凭据（自动判断）`n", "portal_login.log", "UTF-8"
}

; 脚本启动时直接执行登录
AutoLogin()

; 自动登录函数
AutoLogin()
{
    ; 记录日志
    if (enableLogging)
        FileAppend "开始登录过程: " FormatTime(,"yyyy-MM-dd HH:mm:ss") "`n", "portal_login.log", "UTF-8"
    
    try 
    {
        ; 第一步：获取验证码
        if (enableLogging)
            FileAppend "正在获取验证码...`n", "portal_login.log", "UTF-8"
            
        verifyCode := GetVerifyCode()
        if (verifyCode = "")
        {
            if (enableLogging)
                FileAppend "获取验证码失败`n", "portal_login.log", "UTF-8"
            return
        }
        
        if (enableLogging)
            FileAppend "验证码获取成功: " verifyCode "`n", "portal_login.log", "UTF-8"
        
        ; 第二步：进行登录
        loginResult := DoLogin(verifyCode)
        
        ; 处理登录结果
        if (loginResult)
        {
            if (enableLogging)
                FileAppend "登录成功`n", "portal_login.log", "UTF-8"
        }
        else
        {
            if (enableLogging)
                FileAppend "登录失败`n", "portal_login.log", "UTF-8"
        }
    }
    catch as e
    {
        if (enableLogging)
            FileAppend "出错: " e.Message "`n", "portal_login.log", "UTF-8"
    }
}

; 获取验证码函数
GetVerifyCode()
{
    ; 构建请求头
    headers := "Content-Type: application/x-www-form-urlencoded; charset=UTF-8`n"
            . "Accept: */*`n"
            . "Accept-Language: zh-CN,zh-HK;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6`n"
            . "Connection: keep-alive`n"
            . "HTTP_X_REQUESTED_WITH: xmlhttprequest`n"
            . "Origin: " portal_url "`n"
            . "Referer: " portal_url "/portal/local/index.html?uplcyid=1&weburl=`n"
            . "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36`n"
            . "X-Requested-With: XMLHttpRequest"
    
    ; 发送请求获取验证码
    whr := ComObject("WinHttp.WinHttpRequest.5.1")
    whr.Open("POST", verify_code_url, false)
    
    ; 设置请求头
    Loop Parse, headers, "`n"
    {
        if (A_LoopField != "")
        {
            headerParts := StrSplit(A_LoopField, ":", , 2)
            if (headerParts.Length == 2)
                whr.SetRequestHeader(Trim(headerParts[1]), Trim(headerParts[2]))
        }
    }
    
    ; 发送请求
    whr.Send("&submit=submit")
    
    ; 检查响应状态
    if (whr.Status != 200)
        return ""
    
    ; 解析JSON响应
    response := whr.ResponseText
    
    ; 简单解析JSON获取code值
    codePos := InStr(response, '"code":')
    if (codePos)
    {
        startPos := InStr(response, '"', , codePos + 7) + 1
        endPos := InStr(response, '"', , startPos) - 1
        return SubStr(response, startPos, endPos - startPos + 1)
    }
    
    return ""
}

; 登录函数
DoLogin(code)
{
    global encryptedUsername, encryptedPassword
    
    ; 构建请求头
    headers := "Content-Type: application/x-www-form-urlencoded; charset=UTF-8`n"
            . "Accept: */*`n"
            . "Accept-Language: zh-CN,zh-HK;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6`n"
            . "Connection: keep-alive`n"
            . "HTTP_X_REQUESTED_WITH: xmlhttprequest`n"
            . "Origin: " portal_url "`n"
            . "Referer: " portal_url "/portal/local/index.html?uplcyid=1&weburl=`n"
            . "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36`n"
            . "X-Requested-With: XMLHttpRequest"
    
    ; 记录信息
    if (enableLogging)
        FileAppend "使用加密用户名: " encryptedUsername " 密码长度: " StrLen(encryptedPassword) "`n", "portal_login.log", "UTF-8"
    
    ; 构建请求数据
    postData := "username=" encryptedUsername 
             . "&password=" encryptedPassword
             . "&uplcyid=1&language=0&code=" code "&submit=submit"
    
    ; 发送登录请求
    whr := ComObject("WinHttp.WinHttpRequest.5.1")
    whr.Open("POST", login_url, false)
    
    ; 设置请求头
    Loop Parse, headers, "`n"
    {
        if (A_LoopField != "")
        {
            headerParts := StrSplit(A_LoopField, ":", , 2)
            if (headerParts.Length == 2)
                whr.SetRequestHeader(Trim(headerParts[1]), Trim(headerParts[2]))
        }
    }
    
    ; 发送请求
    whr.Send(postData)
    
    ; 记录响应
    if (enableLogging)
        FileAppend "登录响应状态: " whr.Status " 响应内容: " whr.ResponseText "`n", "portal_login.log", "UTF-8"
    
    ; 检查响应状态
    return (whr.Status == 200)
} 