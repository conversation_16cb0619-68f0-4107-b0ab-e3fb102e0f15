﻿#Include C:\Program Files\AutoHotkey\plugin\RapidOcr\RapidOcr.ahk
;截图
#Include ImagePut.ahk

ocr := Rapidocr()
;res := ocr.ocr_from_file("C:\Users\<USER>\Desktop\png.png")
res := ocr.ocr_from_file("C:\Users\<USER>\Desktop\png.png", , true)

for index, block in res {
    ; if (block.boxPoint[1].x == 715 && block.boxPoint[1].y == 215) {
        MsgBox(block.text)
        MsgBox(block.boxPoint[1].x)
        MsgBox(block.boxPoint[1].y)
    ; }

}