﻿; 定义用户名和密码
username := "黄杰"  ; 替换为你的用户名
password := "mxb123"  ; 替换为你的密码


; 打开EasyConnect程序
Run, C:\Program Files (x86)\Sangfor\SSL\EasyConnect\EasyConnect.exe
; 等待微信窗口激活，最长等待时间为10秒
WinWait, ahk_class dlgWindow_1, , 10
if !ErrorLevel
    {
        ; 激活微信窗口
        WinActivate
        
        ; 等待输入框准备好
        Sleep, 1000  ; 等待1秒确保窗口完全加载
        
		; 清除用户名输入框内容
        Send, ^a  ; 全选
        Send, {Del}  ; 删除
        ; 输入用户名
        Send, %username%
        
        ; 模拟按 Tab 键移动到密码输入框
        Send, {Tab}
        
		; 清除密码输入框内容
        Send, ^a  ; 全选
        Send, {Del}  ; 删除
        ; 输入密码
        Send, %password%
        
        ; 模拟按 Enter 键登录
        Send, {Enter}
    }
    else
    {
        MsgBox, EasyConnect窗口未能在10秒内加载
    }
return
