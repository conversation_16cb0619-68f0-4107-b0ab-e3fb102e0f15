﻿#Requires AutoHotkey v2.0
; 定义用户名和密码
username := "黄杰"  ; 替换为你的用户名
password := "mxb123"  ; 替换为你的密码
username1 :="huangjie"
password1 := "Jykj1994@###"
username2 :="huangjie"
password2 := "Sangfor@1234"

; 打开EasyConnect程序
Run "C:\Program Files (x86)\Sangfor\SSL\EasyConnect\EasyConnect.exe"

; 等待EasyConnect窗口激活，最长等待时间为10秒
if WinWait("ahk_class dlgWindow_1", "", 10)
{
    ; 激活EasyConnect窗口
    WinActivate

    ; 等待输入框准备好
    Sleep 1000  ; 等待1秒确保窗口完全加载

    ; 清除用户名输入框内容
    Send "^a"  ; 全选
    Send "{Del}"  ; 删除
    ; 输入用户名
    SendText username

    ; 模拟按 Tab 键移动到密码输入框
    Send "{Tab}"

    ; 清除密码输入框内容
    Send "^a"  ; 全选
    Send "{Del}"  ; 删除
    ; 输入密码
    SendText password

    ; 模拟按 Enter 键登录
    Send "{Enter}"

    Sleep 10000  ; 等待1秒确保窗口完全加载
    ;https://192.168.13.16/fort/login
    Run "chrome.exe https://192.168.13.16/fort/login"
    Sleep 3000  ; 等待1秒确保窗口完全加载
    ; 模拟按下Tab键以跳转到用户名输入框
    Send "{Tab 3}"  ; 假设需要按一次Tab跳转到用户名输入框，实际次数根据网页布局调整

    ; 清除用户名输入框中的内容并输入用户名
    SendText username1  ; 输入用户名
    Sleep 1000  ; 等待1秒，确保网页加载完毕

    ; 模拟按下Tab键以跳转到密码输入框
    Send "{Tab 1}"

    ; 清除密码输入框中的内容并输入密码
    SendText password1  ; 输入密码
    Sleep 1000  ; 等待1秒，确保网页加载完毕
    Send "{Enter}"
    Send "{Enter}"

}
else
{
    MsgBox "EasyConnect窗口未能在10秒内加载"
}
return