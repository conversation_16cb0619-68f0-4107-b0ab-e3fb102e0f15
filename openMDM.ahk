﻿; 定义用户名和密码
username := "admin"  ; 替换为你的用户名
password := "Jykj1994@"  ; 替换为你的密码

; 创建快捷键 Ctrl + L 用来启动浏览器并自动登录

    ; 打开浏览器并访问指定网页
    Run, chrome.exe http://192.168.1.124:8081/index.html#/login
    
    ; 等待浏览器窗口加载
    Sleep, 5000  ; 等待5秒，确保网页加载完毕
	
    ; 模拟按下Tab键以跳转到用户名输入框
    Send, {Tab 1}  ; 假设需要按两次Tab跳转到用户名输入框，实际次数根据网页布局调整
    
    ; 清除用户名输入框中的内容并输入用户名
    Send, ^a  ; 全选
    Send, {Del}  ; 删除
    Send, %username%  ; 输入用户名
	
	Send, {Enter}
    
    ; 模拟按下Tab键以跳转到密码输入框
    Send, {Tab 1}
    
    ; 清除密码输入框中的内容并输入密码
    Send, ^a  ; 全选
    Send, {Del}  ; 删除
    Send, %password%  ; 输入密码
    
    ; 模拟按下Enter键进行登录
    Send, {Enter}
return