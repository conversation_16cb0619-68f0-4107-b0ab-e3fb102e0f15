﻿#Requires AutoHotkey v2.0
#NoTrayIcon

; 检查命令行参数
if (A_Args.Length < 3) {
    MsgBox "请提供必要的参数！`n用法: openMDMV2.ahk username password url"
    ExitApp
}

; 从命令行参数获取值
username := A_Args[1]
password := A_Args[2]
url := A_Args[3]

; 打开浏览器并访问指定网页

; 使用谷歌浏览器打开指定 URL
Run 'chrome.exe "' url '"'

; 等待浏览器窗口加载
Sleep 5000  ; 等待5秒，确保网页加载完毕

; 模拟按下Tab键以跳转到用户名输入框
Send "{Tab 1}"  ; 假设需要按一次Tab跳转到用户名输入框，实际次数根据网页布局调整

; 清除用户名输入框中的内容并输入用户名
Send "^a"  ; 全选
Send "{Del}"  ; 删除
SendText username  ; 输入用户名

; 模拟按下 Enter 键确认用户名
Send "{Enter}"

; 模拟按下Tab键以跳转到密码输入框
Send "{Tab 1}"

; 清除密码输入框中的内容并输入密码
Send "^a"  ; 全选
Send "{Del}"  ; 删除
SendText password  ; 输入密码

; 模拟按下Enter键进行登录
Send "{Enter}"