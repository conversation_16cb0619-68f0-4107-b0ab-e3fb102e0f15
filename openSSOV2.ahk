﻿#Requires AutoHotkey v2.0
#Include C:\Program Files\AutoHotkey\plugin\RapidOcr\RapidOcr.ahk
;截图
#Include ImagePut.ahk

; 定义用户名和密码
username := "admin"  ; 替换为你的用户名
password := "Jykj1994"  ; 替换为你的密码

; 创建快捷键 Ctrl + L 用来启动浏览器并自动登录
{
    ; 打开浏览器并访问指定网页
    Run "chrome.exe https://192.168.1.124:9000/index.html#/login"

    ; 等待浏览器窗口加载
    Sleep 2000  ; 等待5秒，确保网页加载完毕

    ; 获取窗口句柄（查找类名为 Chrome_WidgetWin_1 的窗口）
    ;hwnd := WinExist("ahk_class Chrome_WidgetWin_1")
    ; 指定截取区域的坐标（左上角 x, y 和宽度、长度）
    x := 993   ; 左上角 x 坐标
    y := 421   ; 左上角 y 坐标
    width := 140  ; 截图的宽度
    height := 36 ; 截图的高度
    ; 截图并保存到桌面
    ;ImagePutFile(hwnd,"C:\Users\<USER>\Desktop\png2.png") 
    ;ImagePutWindow([x, y, width, height], "C:\Users\<USER>\Desktop\png.png")
    ImagePutFile([x, y, width, height], "C:\Users\<USER>\Desktop\png2.png")
    ocr := Rapidocr()
    ;res := ocr.ocr_from_file("C:\Users\<USER>\Desktop\png.png")
    res := ocr.ocr_from_file("C:\Users\<USER>\Desktop\png2.png", , true)
    pic := res.text

    ; 使用正则表达式替换非字母和数字字符
   cleanedString := RegExReplace(pic, "[^a-zA-Z0-9]", "")

    ; 模拟按下Tab键以跳转到用户名输入框
    Send "{Tab 1}"  ; 假设需要按一次Tab跳转到用户名输入框，实际次数根据网页布局调整

    ; 清除用户名输入框中的内容并输入用户名
    Send "^a"  ; 全选
    Send "{Del}"  ; 删除
    SendText username  ; 输入用户名
    Sleep 1000  ; 等待1秒，确保网页加载完毕

    ; 模拟按下 Enter 键确认用户名
    Send "{Enter}"

    ; 模拟按下Tab键以跳转到密码输入框
    Send "{Tab 1}"

    ; 清除密码输入框中的内容并输入密码
    Send "^a"  ; 全选
    Send "{Del}"  ; 删除
    SendText password  ; 输入密码
    Sleep 1000  ; 等待1秒，确保网页加载完毕

     ; 模拟按下Tab键以跳转到密码输入框
     Send "{Tab 1}"

    ; 清除密码输入框中的内容并输入密码
    Send "^a"  ; 全选
    Send "{Del}"  ; 删除
    SendText cleanedString  ; 输入密码
    Sleep 1000  ; 等待1秒，确保网页加载完毕


    ; 模拟按下Enter键进行登录
    Send "{Enter}"
}

return