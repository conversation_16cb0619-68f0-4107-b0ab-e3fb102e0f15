﻿; 定义用户名和密码 
username := "18180880365"  ; 替换为你的用户名
password := "&HJcae7a7"  ; 替换为你的密码


; 打开eCloud程序
Run, C:\Program Files (x86)\ecloud\eCloud.exe
; 等待微信窗口激活，最长等待时间为10秒
WinWait, ahk_class FLUTTER_RUNNER_WIN32_WINDOW, , 10
if !ErrorLevel
    {
        ; 激活微信窗口
        WinActivate
        
        ; 等待输入框准备好
        Sleep, 4000  ; 等待1秒确保窗口完全加载
        
        ; 点击短信登录按钮
        ; 使用点击的绝对坐标 (X, Y)，在 Window Spy 中获取坐标
        Click, 790, 127  ; 替换为短信登录按钮的实际坐标
        Sleep, 2000  ; 等待2秒，确保页面切换完成

        ; 点击账号密码登录按钮
        Click, 570, 475  ; 替换为账号密码登录按钮的实际坐标
        Sleep, 2000  ; 等待2秒，确保页面切换完成
        Send, {Tab 2}

		; 清除用户名输入框内容
        Send, ^a  ; 全选
        Send, {Del}  ; 删除
        ; 输入用户名
        Send, %username%
        
        ; 模拟按 Tab 键移动到密码输入框
        Send, {Tab}
        
		; 清除密码输入框内容
        Send, ^a  ; 全选
        Send, {Del}  ; 删除
        ; 输入密码
        Send, %password%
		
		Send, {Enter}
		
		; 点击账号密码登录按钮
        Click, 525, 362  ; 替换为账号密码登录按钮的实际坐标
        Sleep, 2000  ; 等待2秒，确保页面切换完成
        
        ; 模拟按 Enter 键登录
        Send, {Enter}
		
		; 点击账号密码登录按钮
        Click, 678, 316  ; 替换为账号密码登录按钮的实际坐标
        
    }
    else
    {
        MsgBox, EasyConnect窗口未能在10秒内加载
    }
return